"use client";

import { useState, useEffect, useRef, Suspense } from "react";
import { useSearchParams } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { ProductService } from "@/services/product.service";
import { Product } from "@/data/models/product.model";
import { categories } from "@/lib/constants";
import { useCurrency } from "@/components/app/CurrencyProvider";
import { ShopSkeleton } from "@/components/skeletons/shop";
import ReactMarkdown from "react-markdown";
import { Skeleton } from "@/components/ui/skeleton";
import { formatCurrencyWithDigits } from "@/lib/currency";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/index/carousel";
import Autoplay from "embla-carousel-autoplay";

/**
 * Home / Shop page
 *
 * This file implements:
 *  - currency inference (address -> browser locale -> GBP)
 *  - exchange rate fetching
 *  - product listing (existing behavior)
 *  - a floating action button (FAB) that opens a nice country/currency selection dialog
 *    with emoji flags, quick search, and ability to revert to "Auto (inferred)".
 *
 * The dialog uses Radix Dialog wrapper components already available in the project.
 */

/* --- Types --- */
type Banner = {
  title: string;
  subtitle: string;
  productName: string;
  cta1: string;
  cta2: string;
  bgColor: string;
  textColor: string;
  image: string;
};

type Category = {
  id: string;
  name: string;
  parent_id: string | null;
};

type CategoryHierarchy = {
  id: string;
  name: string;
  children: CategoryHierarchy[];
};

// Component that uses useSearchParams (needs to be in Suspense)
function ProductsContent() {
  // Get search params to read category from URL
  const searchParams = useSearchParams();

  // Category hierarchy utility functions
  const buildCategoryHierarchy = (
    categories: Category[],
  ): CategoryHierarchy[] => {
    const categoryMap = new Map<string, CategoryHierarchy>();
    const rootCategories: CategoryHierarchy[] = [];

    // First pass: create all category objects
    categories.forEach((category) => {
      categoryMap.set(category.id, {
        id: category.id,
        name: category.name,
        children: [],
      });
    });

    // Second pass: build hierarchy
    categories.forEach((category) => {
      const categoryNode = categoryMap.get(category.id)!;
      if (category.parent_id === null) {
        rootCategories.push(categoryNode);
      } else {
        const parent = categoryMap.get(category.parent_id);
        if (parent) {
          parent.children.push(categoryNode);
        }
      }
    });

    return rootCategories;
  };

  const getRootCategories = (): Category[] => {
    return categories.filter((category) => category.parent_id === null);
  };
  // UI state
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(
    new Set(),
  );
  const [activeCategoryId, setActiveCategoryId] = useState<string | null>(null);
  const [api, setApi] = useState<any>(null);
  const [current, setCurrent] = useState(0);

  // Read category from URL parameters on component mount
  useEffect(() => {
    const categoryParam = searchParams.get("category");
    if (categoryParam) {
      // Validate that the category exists in our categories list
      const categoryExists = categories.find((cat) => cat.id === categoryParam);
      if (categoryExists) {
        setActiveCategoryId(categoryParam);
      }
    }
  }, [searchParams]);

  useEffect(() => {
    if (!api) {
      return;
    }

    setCurrent(api.selectedScrollSnap() + 1);

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  const banners: Banner[] = [
    // {
    //   "title": "Power Up Your Tech",
    //   "subtitle": "Latest electronics at your fingertips",
    //   "productName": "MailPallet",
    //   "cta1": "View Product",
    //   "cta2": "Shop Products Now",
    //   "bgColor": "bg-black",
    //   "textColor": "text-white",
    //   "image": "https://pykbqzmmchcbcaijhkiq.supabase.co/storage/v1/object/public/mailpallet-assets/banner-assets/Electronic.png"
    // },
    {
      title: "Best Treats from UK Influencers",
      subtitle: "Snacks everyone’s talking about",
      productName: "Influencer Treats Collection",
      cta1: "Discover Now",
      cta2: "Shop Influencer Snacks",
      bgColor: "bg-purple-700",
      textColor: "text-white",
      image:
        "https://pykbqzmmchcbcaijhkiq.supabase.co/storage/v1/object/public/mailpallet-assets/banner-assets/Shades-Niko-Banner.png",
    },
    {
      title: "Best of Snus from the UK",
      subtitle: "Top picks loved across the UK",
      productName: "UK Snus Collection",
      cta1: "Explore Snus",
      cta2: "Shop UK Favourites",
      bgColor: "bg-purple-700",
      textColor: "text-white",
      image:
        "https://pykbqzmmchcbcaijhkiq.supabase.co/storage/v1/object/public/mailpallet-assets/banner-assets/Snus-18-Banner.png",
    },
    {
      title: "Elevate Your Wardrobe",
      subtitle: "Fresh fashion for every occasion",
      productName: "MailPallet",
      cta1: "View Product",
      cta2: "Shop Products Now",
      bgColor: "bg-black",
      textColor: "text-white",
      image:
        "https://pykbqzmmchcbcaijhkiq.supabase.co/storage/v1/object/public/mailpallet-assets/banner-assets/Fashion_Banner.png",
    },
    // {
    //   "title": "Glow Inside & Out",
    //   "subtitle": "Health and beauty essentials",
    //   "productName": "MailPallet",
    //   "cta1": "View Product",
    //   "cta2": "Shop Products Now",
    //   "bgColor": "bg-black",
    //   "textColor": "text-white",
    //   "image": "https://pykbqzmmchcbcaijhkiq.supabase.co/storage/v1/object/public/mailpallet-assets/banner-assets/H_AND_B.png"
    // },
    // {
    //   "title": "Fast Local Delivery",
    //   "subtitle": "Shop now!",
    //   "productName": "MailPallet",
    //   "cta1": "View Product",
    //   "cta2": "Shop Products Now",
    //   "bgColor": "bg-black",
    //   "textColor": "text-white",
    //   "image": "https://pykbqzmmchcbcaijhkiq.supabase.co/storage/v1/object/public/mailpallet-assets/banner-assets/Local_Delivery.png"
    // },
    {
      title: "Skip the Middleman",
      subtitle: "Direct deals, better value",
      productName: "MailPallet",
      cta1: "View Product",
      cta2: "Shop Products Now",
      bgColor: "bg-black",
      textColor: "text-white",
      image:
        "https://pykbqzmmchcbcaijhkiq.supabase.co/storage/v1/object/public/mailpallet-assets/banner-assets/Skip_the_Middle_Man_Banner.png",
    },
  ];

  // Products & currency state
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const productsPerPage = 12; // Adjust this number as needed

  // Currency state is provided by CurrencyProvider (useCurrency)
  const { currencyCode, exchangeRate, isLoadingExchangeRate } = useCurrency();

  // Keep legacy variable name used by rendering logic for minimal changes
  const userCurrencyCode = currencyCode;

  // Pricing helpers
  const getDisplayPricing = (product: Product) => {
    if (product.variants?.variants && product.variants.variants.length > 0) {
      const first = product.variants.variants[0];
      return {
        price: first.price || product.primary_data.price,
        sale_price: first.sale_price || product.primary_data.sale_price,
        hasVariants: true,
      };
    }
    return {
      price: product.primary_data.price,
      sale_price: product.primary_data.sale_price,
      hasVariants: false,
    };
  };

  const convertPrice = (price: number) => {
    if (userCurrencyCode === "GBP" || !exchangeRate) return price;
    return price * exchangeRate;
  };

  // Category helpers
  const toggleCategoryExpansion = (id: string) => {
    const next = new Set(expandedCategories);
    if (next.has(id)) next.delete(id);
    else next.add(id);
    setExpandedCategories(next);
  };

  const handleCategorySelect = (id: string | null) => {
    setActiveCategoryId(id);
    setCurrentPage(1); // Reset to first page when category changes
  };

  // Helper function to handle page changes with scroll to top
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Smooth scroll to top of the page
    window.scrollTo({
      top: 500,
      behavior: "smooth",
    });
  };

  // Get root categories for navigation
  const rootCategories = getRootCategories();
  const categoryHierarchy = buildCategoryHierarchy(categories);

  // Fetch products listing
  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true);
      try {
        const productService = new ProductService();
        const selectedCategory = categories.find(
          (c) => c.id === activeCategoryId,
        );
        const options = activeCategoryId
          ? selectedCategory && selectedCategory.parent_id
            ? { subcategory: activeCategoryId, limit: 1000 }
            : { category: activeCategoryId, limit: 1000 }
          : { limit: 1000 };
        const { data } = await productService.listProducts(options);

        // If no category is selected, randomize order to surface varied products
        if (!activeCategoryId) {
          const shuffled = [...data];
          for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
          }
          setProducts(shuffled);
        } else {
          setProducts(data);
        }
      } catch (err) {
        console.error("Error fetching products:", err);
      } finally {
        setLoading(false);
      }
    };
    fetchProducts();
  }, [activeCategoryId]);

  // Stock helper
  const getStockStatus = (product: Product) => {
    if (
      product.type === "internal" &&
      product.details &&
      "stock" in product.details &&
      typeof product.details.stock === "number"
    ) {
      const stockLevel = product.details.stock;
      if (stockLevel > 10)
        return { text: "In Stock", classes: "bg-green-100 text-green-800" };
      if (stockLevel > 0)
        return {
          text: `Only ${stockLevel} left`,
          classes: "bg-yellow-100 text-yellow-800",
        };
      return { text: "Out of Stock", classes: "bg-red-100 text-red-800" };
    }
    return null;
  };

  // Pagination calculations
  const totalProducts = products.length;
  const totalPages = Math.ceil(totalProducts / productsPerPage);
  const showPagination = totalProducts > productsPerPage;

  // Get current page products
  const currentProducts = products.slice(
    (currentPage - 1) * productsPerPage,
    currentPage * productsPerPage,
  );

  return (
    <>
      {/* Full-width Hero Banner - Desktop Only */}
      <div className="hidden md:block w-screen relative left-1/2 right-1/2 -ml-[50vw] -mr-[50vw] mb-8 md:mb-12">
        <Carousel
          setApi={setApi}
          className="w-full overflow-hidden"
          opts={{
            loop: true,
          }}
          plugins={[
            Autoplay({
              delay: 5000,
            }),
          ]}
        >
          <CarouselContent>
            {banners.map((banner, index) => (
              <CarouselItem key={index} className="pl-0">
                <div
                  className="relative h-96 lg:h-[500px] overflow-hidden bg-cover bg-center bg-no-repeat transition-all duration-1000"
                  style={{
                    backgroundImage: `url(${banner.image})`,
                  }}
                >
                  {/* Dark overlay for better text readability */}
                  <div className="absolute inset-0 bg-black bg-opacity-0"></div>

                  <div className="max-w-7xl mx-auto px-6 md:px-8 lg:px-12 h-full flex items-center relative z-10">
                    <div className="w-full text-left">
                      <div className="space-y-6 max-w-2xl">
                        <h1 className="text-4xl lg:text-5xl xl:text-6xl font-bold text-white leading-tight drop-shadow-lg">
                          {banner.title}
                          <br />
                          <span className="text-blue-300">
                            {banner.subtitle}
                          </span>
                        </h1>
                      </div>
                    </div>
                  </div>
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-3">
            {banners.map((_, index) => (
              <button
                key={index}
                onClick={() => api.scrollTo(index)}
                className={`h-3 rounded-full transition-all duration-300 shadow-md ${
                  current === index + 1
                    ? "bg-white w-8"
                    : "bg-white bg-opacity-50 hover:bg-opacity-75 w-3"
                }`}
                aria-label={`Go to banner ${index + 1}`}
              />
            ))}
          </div>
        </Carousel>
      </div>

      <main className="max-w-7xl mx-auto px-3 sm:px-4 py-6 sm:py-8 lg:px-8">
        {/* Mobile Horizontal Scrollable Categories - Improved spacing */}
        <div className="md:hidden mb-4 overflow-x-auto scrollbar-hide">
          <div className="flex space-x-2 sm:space-x-4 py-2 px-1 min-w-full">
            {/* All Products button */}
            <button
              className={`flex-shrink-0 py-1 sm:py-2 px-3 sm:px-4 rounded-full text-xs sm:text-sm font-medium whitespace-nowrap transition-colors ${
                activeCategoryId === null
                  ? "bg-gray-900 text-white"
                  : "bg-gray-100 text-gray-800 hover:bg-gray-200"
              }`}
              onClick={() => handleCategorySelect(null)}
            >
              All Products
            </button>
            {/* Category buttons */}
            {rootCategories.map((category) => (
              <button
                key={category.id}
                className={`flex-shrink-0 py-1 sm:py-2 px-3 sm:px-4 rounded-full text-xs sm:text-sm font-medium whitespace-nowrap transition-colors ${
                  activeCategoryId === category.id
                    ? "bg-gray-900 text-white"
                    : "bg-gray-100 text-gray-800 hover:bg-gray-200"
                }`}
                onClick={() => handleCategorySelect(category.id)}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-8 lg:gap-6">
          {/* Sidebar Navigation - Desktop */}
          <aside className="hidden md:block w-full md:w-64 flex-shrink-0 lg:sticky lg:top-0 self-start">
            <nav className="space-y-4">
              <div className="pb-2">
                <button
                  onClick={() => handleCategorySelect(null)}
                  className={`block py-2 font-medium transition-colors w-full text-left ${
                    activeCategoryId === null
                      ? "text-blue-600"
                      : "text-gray-900 hover:text-gray-600"
                  }`}
                >
                  All Products
                </button>
              </div>

              <div className="pb-2 border-b">
                <div className="py-2 font-medium">
                  <span>Categories</span>
                </div>
              </div>

              <div className="space-y-2">
                {categoryHierarchy.map((cat) => (
                  <div key={cat.id} className="space-y-1">
                    <div className="flex items-center justify-between">
                      <button
                        onClick={() => handleCategorySelect(cat.id)}
                        className={`flex-1 text-left py-1 font-medium transition-colors ${
                          activeCategoryId === cat.id
                            ? "text-blue-600"
                            : "text-gray-900 hover:text-gray-600"
                        }`}
                      >
                        {cat.name}
                      </button>
                      {cat.children.length > 0 && (
                        <button
                          onClick={() => toggleCategoryExpansion(cat.id)}
                          className="p-1 hover:bg-gray-100 rounded"
                        >
                          <svg
                            className={`w-3 h-3 transition-transform ${expandedCategories.has(cat.id) ? "rotate-90" : ""}`}
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                          >
                            <path
                              strokeWidth={2}
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="M9 5l7 7-7 7"
                            ></path>
                          </svg>
                        </button>
                      )}
                    </div>

                    {expandedCategories.has(cat.id) &&
                      cat.children.length > 0 && (
                        <div className="pl-4 space-y-1">
                          {cat.children.map((sub) => (
                            <button
                              key={sub.id}
                              onClick={() => handleCategorySelect(sub.id)}
                              className={`flex items-center gap-2 py-1 w-full text-left transition-colors ${
                                activeCategoryId === sub.id
                                  ? "text-blue-600"
                                  : "text-gray-700 hover:text-gray-600"
                              }`}
                            >
                              <span className="text-gray-400">•</span>
                              <span className="text-sm">{sub.name}</span>
                            </button>
                          ))}
                        </div>
                      )}
                  </div>
                ))}
              </div>
            </nav>
          </aside>

          <div className="flex-1">
            {loading ? (
              <ShopSkeleton />
            ) : products.length === 0 ? (
              <div className="py-12 text-center text-gray-500 h-screen">
                No products returned
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                {currentProducts.map((product) => {
                  const stockStatus = getStockStatus(product);
                  const pricing = getDisplayPricing(product);
                  return (
                    <Link
                      key={product.id}
                      href={`/home/<USER>/products/${product.id}`}
                      className="group bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 overflow-hidden border border-gray-100 block"
                    >
                      <div className="relative">
                        {/* Discount badge */}
                        {(() => {
                          const discountPercent =
                            pricing.sale_price &&
                            pricing.sale_price < pricing.price
                              ? Math.round(
                                  ((pricing.price - pricing.sale_price) /
                                    pricing.price) *
                                    100,
                                )
                              : 0;
                          return discountPercent > 0 ? (
                            <div className="absolute top-2 left-2 z-10 bg-red-500 text-white text-xs font-bold py-1 px-2 rounded-full">
                              {discountPercent}% OFF
                            </div>
                          ) : null;
                        })()}

                        <div className="w-full h-48 sm:h-56 md:h-64 overflow-hidden">
                          <Image
                            src={
                              product.variants?.variants?.[0]?.images?.[0]
                                ?.url ||
                              product.primary_image?.url ||
                              "https://placehold.co/400x300"
                            }
                            alt={product.title}
                            width={400}
                            height={300}
                            className="w-full h-full object-contain object-center transform group-hover:scale-105 transition-transform duration-500"
                          />
                        </div>
                      </div>

                      <div className="p-3 sm:p-4">
                        <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-1 sm:mb-2 line-clamp-1 group-hover:text-blue-600 transition-colors">
                          {product.title}
                        </h3>

                        <div className="text-xs sm:text-sm text-gray-500 mb-3 sm:mb-4 line-clamp-2">
                          <ReactMarkdown className="prose prose-sm max-w-none dark:prose-invert">
                            {product.description}
                          </ReactMarkdown>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-end gap-1 sm:gap-2">
                            {pricing.sale_price &&
                            pricing.sale_price < pricing.price ? (
                              <>
                                {isLoadingExchangeRate &&
                                userCurrencyCode !== "GBP" ? (
                                  <>
                                    <Skeleton className="w-24 h-6" />
                                    <Skeleton className="w-20 h-4" />
                                  </>
                                ) : (
                                  <>
                                    <span className="text-lg sm:text-xl font-bold text-gray-900">
                                      {formatCurrencyWithDigits(
                                        convertPrice(pricing.sale_price),
                                        userCurrencyCode,
                                        0,
                                      )}
                                    </span>
                                    <span className="text-sm sm:text-base text-gray-500 line-through">
                                      {formatCurrencyWithDigits(
                                        convertPrice(pricing.price),
                                        userCurrencyCode,
                                        0,
                                      )}
                                    </span>
                                  </>
                                )}
                              </>
                            ) : isLoadingExchangeRate &&
                              userCurrencyCode !== "GBP" ? (
                              <Skeleton className="w-24 h-6" />
                            ) : (
                              <span className="text-lg sm:text-xl font-bold text-gray-900">
                                {formatCurrencyWithDigits(
                                  convertPrice(pricing.price),
                                  userCurrencyCode,
                                  0,
                                )}
                              </span>
                            )}
                          </div>

                          {stockStatus && (
                            <span
                              className={`text-xs px-2 py-1 rounded ${stockStatus.classes}`}
                            >
                              {stockStatus.text}
                            </span>
                          )}
                        </div>
                      </div>
                    </Link>
                  );
                })}
              </div>
            )}

            {/* Pagination */}
            {showPagination && (
              <div className="mt-8 flex flex-col items-center space-y-4">
                {/* Results info */}
                <div className="text-sm text-gray-600 text-center">
                  Showing {(currentPage - 1) * productsPerPage + 1} to{" "}
                  {Math.min(currentPage * productsPerPage, totalProducts)} of{" "}
                  {totalProducts} products
                </div>

                {/* Pagination controls */}
                <div className="flex items-center justify-center">
                  {/* Mobile pagination - simplified */}
                  <div className="flex md:hidden items-center space-x-2">
                    <button
                      onClick={() =>
                        handlePageChange(Math.max(1, currentPage - 1))
                      }
                      disabled={currentPage === 1}
                      className="flex items-center justify-center w-10 h-10 rounded-lg border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      aria-label="Previous page"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 19l-7-7 7-7"
                        />
                      </svg>
                    </button>

                    <div className="flex items-center space-x-1 px-3 py-2 bg-gray-50 rounded-lg">
                      <span className="text-sm font-medium text-gray-900">
                        {currentPage}
                      </span>
                      <span className="text-sm text-gray-500">of</span>
                      <span className="text-sm font-medium text-gray-900">
                        {totalPages}
                      </span>
                    </div>

                    <button
                      onClick={() =>
                        handlePageChange(Math.min(totalPages, currentPage + 1))
                      }
                      disabled={currentPage === totalPages}
                      className="flex items-center justify-center w-10 h-10 rounded-lg border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      aria-label="Next page"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 5l7 7-7 7"
                        />
                      </svg>
                    </button>
                  </div>

                  {/* Desktop pagination - full controls */}
                  <div className="hidden md:flex items-center space-x-1">
                    <button
                      onClick={() =>
                        handlePageChange(Math.max(1, currentPage - 1))
                      }
                      disabled={currentPage === 1}
                      className="flex items-center justify-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      Previous
                    </button>

                    {/* Page numbers */}
                    {(() => {
                      const pages = [];
                      const maxVisiblePages = 7;
                      let startPage = Math.max(
                        1,
                        currentPage - Math.floor(maxVisiblePages / 2),
                      );
                      let endPage = Math.min(
                        totalPages,
                        startPage + maxVisiblePages - 1,
                      );

                      // Adjust start page if we're near the end
                      if (endPage - startPage + 1 < maxVisiblePages) {
                        startPage = Math.max(1, endPage - maxVisiblePages + 1);
                      }

                      // Always show first page
                      if (startPage > 1) {
                        pages.push(
                          <button
                            key={1}
                            onClick={() => handlePageChange(1)}
                            className="flex items-center justify-center w-10 h-10 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                          >
                            1
                          </button>,
                        );
                        if (startPage > 2) {
                          pages.push(
                            <span
                              key="ellipsis1"
                              className="flex items-center justify-center w-10 h-10 text-gray-500"
                            >
                              ...
                            </span>,
                          );
                        }
                      }

                      // Page numbers
                      for (let i = startPage; i <= endPage; i++) {
                        pages.push(
                          <button
                            key={i}
                            onClick={() => handlePageChange(i)}
                            className={`flex items-center justify-center w-10 h-10 text-sm font-medium rounded-lg transition-colors ${
                              currentPage === i
                                ? "bg-blue-600 text-white border border-blue-600"
                                : "text-gray-700 bg-white border border-gray-300 hover:bg-gray-50"
                            }`}
                          >
                            {i}
                          </button>,
                        );
                      }

                      // Always show last page
                      if (endPage < totalPages) {
                        if (endPage < totalPages - 1) {
                          pages.push(
                            <span
                              key="ellipsis2"
                              className="flex items-center justify-center w-10 h-10 text-gray-500"
                            >
                              ...
                            </span>,
                          );
                        }
                        pages.push(
                          <button
                            key={totalPages}
                            onClick={() => handlePageChange(totalPages)}
                            className="flex items-center justify-center w-10 h-10 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                          >
                            {totalPages}
                          </button>,
                        );
                      }

                      return pages;
                    })()}

                    <button
                      onClick={() =>
                        handlePageChange(Math.min(totalPages, currentPage + 1))
                      }
                      disabled={currentPage === totalPages}
                      className="flex items-center justify-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      Next
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </>
  );
}

// Main component with Suspense boundary
export default function Products() {
  return (
    <Suspense fallback={<ShopSkeleton />}>
      <ProductsContent />
    </Suspense>
  );
}
