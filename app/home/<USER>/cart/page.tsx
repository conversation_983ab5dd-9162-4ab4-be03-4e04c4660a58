"use client";

import React, { useEffect, useMemo, useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import {
  useCart,
  useCartDispatch,
  removeFromCart,
  updateQuantity,
  clearCart,
} from "@/lib/CartContext";
import Image from "next/image";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { TriangleAlert } from "lucide-react";
import { CartProduct } from "@/lib/types";
import { StripePaymentOrder } from "@/components/app/stripe-payment-order";
import type { OrderItem, Currency } from "@/data/models/order.model";
import { UserService } from "@/services/user.service";
import { AddressService, UserAddressModel } from "@/services/address.service";
import { useCurrency } from "@/components/app/CurrencyProvider";
import { useAuth } from "@/hooks/use-auth";
import { packItemsIntoParcels } from "@/lib/shipping";
import {
  formatCurrency,
  formatCurrencyWithDigits,
  formatCurrencyFallback,
  roundToCurrency,
} from "@/lib/currency";

/**
 * Simplified single-origin checkout page.
 *
 * Changes:
 * - Treat cart as single-origin (origin from first item).
 * - Use packer-derived parcels/prices as canonical shipping amounts.
 * - Aggregate per-item legacy duty and attach duty at order-item level.
 * - Remove converted* local state (currency handled by CurrencyProvider).
 * - Replace earlier `shippingOption` with `computedOrder` object containing
 *   shipping_gbp, duty_gbp, parcels, country, etc.
 * - Replace `calculatePaymentTotals` with `buildOrderForPayment`.
 */

type ComputedOrder = {
  available: boolean; // false indicates packer failed / shipping unavailable
  country?: string;
  parcels?: any[]; // parcel summaries from packer
  shipping_gbp: number; // aggregated shipping GBP derived from packer/tier overrides
  duty_gbp: number; // aggregated legacy duty GBP
  days?: number | undefined;
};

const CheckoutPage: React.FC = () => {
  const cart = useCart();
  const dispatch = useCartDispatch();
  const router = useRouter();

  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [isLoadingUser, setIsLoadingUser] = useState(true);
  const [isMounted, setIsMounted] = useState(false);
  const [userAddress, setUserAddress] = useState<UserAddressModel | null>(null);

  // Computed order (packer-derived shipping + duty)
  const [computedOrder, setComputedOrder] = useState<ComputedOrder | undefined>(
    undefined,
  );

  // Services & auth
  const userService = new UserService();
  const addressService = new AddressService();
  const {
    isAuthenticated,
    redirectIfNotAuthenticated,
    loading: authLoading,
    user,
  } = useAuth();

  // Currency provider
  const {
    currencyCode: userCurrencyCode,
    exchangeRate,
    isLoadingExchangeRate,
    displayCurrencyCode,
  } = useCurrency();

  // Mount guard to avoid hydration mismatch
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Load profile and address
  const loadUserProfile = async () => {
    try {
      await userService.getUserProfile();
    } catch (err) {
      console.error("Failed to load user profile:", err);
    } finally {
      setIsLoadingUser(false);
    }
  };

  const loadUserAddress = async () => {
    try {
      const addr = await addressService.fetchUserAddress();
      setUserAddress(addr);
    } catch (err) {
      console.error("Failed to load user address:", err);
    }
  };

  useEffect(() => {
    if (!isMounted) return;

    if (!authLoading && !isAuthenticated) {
      redirectIfNotAuthenticated();
      return;
    }

    if (isAuthenticated) loadUserProfile();
    loadUserAddress();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [authLoading, isAuthenticated, isMounted]);

  // Helpers
  const getCartItemPricing = (item: CartProduct) => {
    if (item.selectedVariant?.price !== undefined) {
      return {
        price: item.selectedVariant.price,
        sale_price:
          item.selectedVariant.sale_price || item.selectedVariant.price,
        stock: item.selectedVariant.stock || 0,
      };
    }
    return {
      price: item.primary_data.price,
      sale_price: item.primary_data.sale_price || item.primary_data.price,
      stock: 0,
    };
  };

  const areAllAddressFlagsNull = (): boolean => {
    return (
      userAddress !== null &&
      userAddress.WithinAccra === null &&
      userAddress.OutsideAccra === null &&
      userAddress.WithinLagos === null &&
      userAddress.OutsideLagos === null
    );
  };

  const isAddressInSupportedCountry = (): boolean => {
    if (!userAddress || !userAddress.DeliverTo) return false;
    const supported = ["Ghana", "Nigeria"];
    return supported.includes(userAddress.DeliverTo);
  };

  // Cart items and subtotal (GBP amounts are product-native currency assumed to be GBP per previous behavior)
  const cartItems = cart.items;
  const hasItems = cartItems.length > 0;
  const origin = hasItems ? cartItems[0].origin_location : "Unknown";

  const subtotalGbp = cartItems.reduce((sum, item) => {
    const pricing = getCartItemPricing(item);
    return sum + pricing.sale_price * item.quantity;
  }, 0);

  // Build shippable items from the cart for the packer
  const shippableItems = useMemo(() => {
    return cartItems.map((item) => {
      const internalDetails =
        item.type === "internal" ? (item.details as any) : null;
      const rawDim = internalDetails?.dimensions_cm ?? null;
      const dimensions_cm =
        rawDim &&
        typeof rawDim.length === "number" &&
        typeof rawDim.width === "number" &&
        typeof rawDim.height === "number"
          ? {
              length: Number(rawDim.length),
              width: Number(rawDim.width),
              height: Number(rawDim.height),
            }
          : null;

      const weight_kg =
        typeof internalDetails?.weight_kg === "number" &&
        isFinite(internalDetails.weight_kg)
          ? Number(internalDetails.weight_kg)
          : null;

      const qty =
        typeof item.quantity === "number" &&
        Number.isFinite(item.quantity) &&
        item.quantity > 0
          ? Math.floor(item.quantity)
          : 1;

      return {
        id: item.id,
        dimensions_cm,
        weight_kg,
        quantity: qty,
      };
    });
  }, [cartItems]);

  // Legacy per-item duty lookup (per-unit duty) based on user's DeliverTo
  const legacyDutyByItemId = useMemo(() => {
    const map: { [key: string]: number } = {};
    const userCountry = userAddress?.DeliverTo || "United Kingdom";
    cartItems.forEach((item) => {
      let dutyPerUnit = 0;
      if (item.shipping_rates && item.shipping_rates.length > 0) {
        const match =
          item.shipping_rates.find(
            (rate: any) =>
              rate.to_country === userCountry || rate.to_zone === userCountry,
          ) || item.shipping_rates[0];
        dutyPerUnit = (match as any)?.duty || 0;
      }
      map[item.id] = dutyPerUnit;
    });
    return map;
  }, [cartItems, userAddress]);

  // Compute computedOrder using packer (packer-derived shipping and aggregated duty)
  useEffect(() => {
    if (!hasItems) {
      setComputedOrder(undefined);
      return;
    }

    const userCountry = userAddress?.DeliverTo || "United Kingdom";

    try {
      const packResult = packItemsIntoParcels(shippableItems);
      const parcels = packResult.parcels || [];

      const shippingGbp = parcels.reduce((sum: number, parcel: any) => {
        let base = parcel.basePriceGbp || 0;
        if (parcel.tier) {
          if (
            userCountry === "Ghana" &&
            typeof parcel.tier.ghPriceGbp === "number"
          ) {
            base = parcel.tier.ghPriceGbp;
          } else if (
            userCountry === "Nigeria" &&
            typeof parcel.tier.ngPriceGbp === "number"
          ) {
            base = parcel.tier.ngPriceGbp;
          } else if (typeof parcel.tier.priceGbp === "number") {
            base = parcel.tier.priceGbp;
          }
        }
        return sum + base;
      }, 0);

      const dutyGbp = cartItems.reduce((sum, item) => {
        const perUnit = legacyDutyByItemId[item.id] || 0;
        return sum + perUnit * item.quantity;
      }, 0);

      setComputedOrder({
        available: true,
        country: userCountry,
        parcels,
        shipping_gbp: shippingGbp,
        duty_gbp: dutyGbp,
        days: cartItems[0]?.shipping_rates?.[0]?.estimated_delivery_days,
      });
    } catch (err) {
      console.warn("Packer failed:", err);
      setComputedOrder({ available: false, shipping_gbp: 0, duty_gbp: 0 });
    }
  }, [shippableItems, cartItems, userAddress, legacyDutyByItemId]);

  // Transform cart items into order items and include duty on each order line
  const transformCartToOrderItems = (): Array<
    OrderItem & { duty?: number }
  > => {
    return cartItems.map((item) => {
      const pricing = getCartItemPricing(item);
      const orderItem: OrderItem & { duty?: number } = {
        id: item.id,
        product_id: item.id,
        product_snapshot: {
          title: item.title,
          description: item.description,
          category: item.category,
          subcategory: item.subcategory,
          refundable: item.refundable,
          condition: item.condition,
          sku: item.type === "internal" ? item.id : item.id,
          weight_kg:
            item.type === "internal"
              ? (item.details as any).weight_kg
              : undefined,
          dimensions_cm:
            item.type === "internal" &&
            (item.details as any).dimensions_cm &&
            (item.details as any).dimensions_cm.length !== undefined &&
            (item.details as any).dimensions_cm.width !== undefined &&
            (item.details as any).dimensions_cm.height !== undefined
              ? {
                  length: (item.details as any).dimensions_cm.length as number,
                  width: (item.details as any).dimensions_cm.width as number,
                  height: (item.details as any).dimensions_cm.height as number,
                }
              : undefined,
        },
        quantity: item.quantity,
        unit_price: pricing.sale_price,
        line_total: pricing.sale_price * item.quantity,
        currency: (item.currency || "GBP") as Currency,
        warehouse_location: item.origin_location,
        created_at: new Date(),
        updated_at: new Date(),
      };

      if (item.selectedVariant && item.selectedVariant.variant_id) {
        orderItem.variant_id = item.selectedVariant.variant_id;
        orderItem.variant_snapshot = {
          variant_id: item.selectedVariant.variant_id,
          option_values: item.selectedVariantOptions || {},
          sku: item.selectedVariant.sku,
          image:
            item.selectedVariant.images &&
            item.selectedVariant.images.length > 0
              ? item.selectedVariant.images[0]
              : { url: "", name: "" },
          price: item.selectedVariant.price || pricing.price,
          sale_price: item.selectedVariant.sale_price,
          stock: item.selectedVariant.stock || 0,
        } as any;
      }

      // attach duty per-line (per-unit duty * quantity)
      const perUnitDuty = legacyDutyByItemId[item.id] || 0;
      orderItem.duty = perUnitDuty * item.quantity;

      return orderItem;
    });
  };

  // Build order object for payment (uses packer-derived shipping & duty)
  const buildOrderForPayment = () => {
    const orderItems = transformCartToOrderItems();
    const subtotal = orderItems.reduce(
      (sum, it) => sum + (it.line_total || 0),
      0,
    );

    const shipping =
      computedOrder && computedOrder.available ? computedOrder.shipping_gbp : 0;
    const duty =
      computedOrder && computedOrder.available ? computedOrder.duty_gbp : 0;
    const taxAmount = 0;
    const total = subtotal + shipping + duty;

    return {
      orderItems,
      subtotal,
      shippingCost: shipping,
      duty,
      taxAmount,
      totalAmount: total,
      currency: "GBP" as Currency,
      meta: {
        origin,
        country: computedOrder?.country,
        parcels: computedOrder?.parcels,
        shipping_available: computedOrder ? computedOrder.available : undefined,
        estimated_days: computedOrder?.days,
      },
    };
  };

  // Payment handlers
  const handleProceedToPayment = () => {
    if (!user) {
      router.push(`/login?next=${encodeURIComponent("/home/<USER>/cart")}`);
      return;
    }

    if (!computedOrder || computedOrder.available === false) {
      // shipping unavailable - do nothing (UI button will be disabled)
      return;
    }

    setIsPaymentModalOpen(true);
  };

  const handleClosePaymentModal = () => setIsPaymentModalOpen(false);

  const handlePaymentSuccess = () => {
    clearCart(dispatch);
    setIsPaymentModalOpen(false);
    router.push("/home/<USER>/my-orders");
  };

  // UI rendering
  if (!isMounted) {
    return (
      <div className="container mx-auto px-4 py-6 sm:py-8">
        <h1 className="text-2xl sm:text-3xl font-bold mb-6 sm:mb-8">
          Checkout
        </h1>
        <div className="flex justify-center items-center py-12">
          <div className="text-center">Loading...</div>
        </div>
      </div>
    );
  }

  // Utility to format GBP numbers into display currency using exchangeRate when needed.
  const displayAmount = (amountGbp: number) => {
    if (!exchangeRate || displayCurrencyCode === "GBP") {
      return formatCurrency(amountGbp, "GBP");
    }
    const converted = roundToCurrency(
      amountGbp * exchangeRate,
      displayCurrencyCode,
    );
    return formatCurrency(converted, displayCurrencyCode);
  };

  return (
    <div className="container mx-auto px-4 py-6 sm:py-8">
      <h1 className="text-2xl sm:text-3xl font-bold mb-6 sm:mb-8">Checkout</h1>

      {cartItems.length === 0 ? (
        <div className="text-center py-8 sm:py-12">
          <h2 className="text-xl font-medium mb-4">Your cart is empty</h2>
          <p className="text-gray-600 mb-6">
            Add some items to your cart to get started.
          </p>
          <Link
            href="/home/<USER>"
            className="inline-block px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Continue Shopping
          </Link>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
          <div className="lg:col-span-2 space-y-6">
            <div className="border rounded-lg overflow-hidden shadow-sm">
              <div className="bg-gray-100 px-4 py-3 flex justify-between items-center">
                <h2 className="font-bold text-base sm:text-lg">
                  Items ({cartItems.length})
                </h2>
                <span className="text-sm text-gray-600">
                  Shipping from {origin}
                </span>
              </div>

              <div className="divide-y">
                {cartItems.map((item) => {
                  const imageUrl =
                    item.selectedVariant?.images?.[0]?.url ||
                    item.primary_image?.url ||
                    "/placeholder-image.png";
                  const pricing = getCartItemPricing(item);

                  return (
                    <div
                      key={`${item.id}-${item.selectedVariant?.variant_id || "no-variant"}`}
                      className="p-4 flex flex-col sm:flex-row"
                    >
                      <div className="relative w-20 h-20 sm:w-24 sm:h-24 sm:mr-4 mb-4 sm:mb-0 flex-shrink-0">
                        <Image
                          src={imageUrl}
                          alt={item.title}
                          className="object-center object-contain w-full h-full rounded"
                          width={96}
                          height={96}
                          onError={(e) => {
                            (e.target as HTMLImageElement).src =
                              "/placeholder-image.png";
                          }}
                        />
                      </div>

                      <div className="flex-1">
                        <div className="flex flex-col sm:flex-row sm:justify-between mb-2">
                          <h3 className="font-medium text-base sm:text-lg mb-1 sm:mb-0">
                            {item.title}
                          </h3>
                          <div className="text-left sm:text-right">
                            <div>
                              {isLoadingExchangeRate &&
                              userCurrencyCode !== "GBP" ? (
                                <div className="flex flex-col items-end gap-1">
                                  <Skeleton className="h-4 w-20" />
                                  <Skeleton className="h-5 w-24" />
                                </div>
                              ) : pricing.sale_price < pricing.price ? (
                                <>
                                  <span className="text-gray-500 line-through text-sm mr-2">
                                    {displayAmount(pricing.price)}
                                  </span>
                                  <span className="font-bold">
                                    {displayAmount(pricing.sale_price)}
                                  </span>
                                </>
                              ) : (
                                <span className="font-bold">
                                  {displayAmount(pricing.sale_price)}
                                </span>
                              )}
                            </div>
                            {pricing.sale_price < pricing.price && (
                              <span className="text-green-600 text-sm">
                                Save{" "}
                                {Math.round(
                                  ((pricing.price - pricing.sale_price) /
                                    pricing.price) *
                                    100,
                                )}
                                %
                              </span>
                            )}
                          </div>
                        </div>

                        {item.selectedVariantOptions &&
                          Object.keys(item.selectedVariantOptions).length >
                            0 && (
                            <div className="text-sm text-gray-600 mb-2">
                              {Object.entries(item.selectedVariantOptions).map(
                                ([key, value]) => (
                                  <span
                                    key={key}
                                    className="inline-block bg-gray-100 rounded px-2 py-1 mr-2 mb-1"
                                  >
                                    {key}: {value}
                                  </span>
                                ),
                              )}
                            </div>
                          )}

                        <div className="text-sm text-gray-600 mb-3">
                          {item.type === "internal" ? (
                            <span>
                              In Stock:{" "}
                              {getCartItemPricing(item).stock || "Available"}
                            </span>
                          ) : (
                            <span>
                              From: {(item.details as any).origin_name}
                            </span>
                          )}
                          {" • "}
                          <span>Condition: {item.condition}</span>
                        </div>

                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 sm:gap-0">
                          <div className="flex items-center">
                            <button
                              className="w-8 h-8 flex items-center justify-center border rounded-l hover:bg-gray-100"
                              disabled={item.quantity <= 1}
                              onClick={() =>
                                updateQuantity(
                                  dispatch,
                                  item,
                                  item.quantity - 1,
                                )
                              }
                            >
                              -
                            </button>
                            <input
                              type="number"
                              value={item.quantity}
                              className="w-12 h-8 text-center border-t border-b"
                              min="1"
                              onChange={(e) => {
                                const newQuantity =
                                  parseInt(e.target.value) || 1;
                                updateQuantity(dispatch, item, newQuantity);
                              }}
                            />
                            <button
                              className="w-8 h-8 flex items-center justify-center border rounded-r hover:bg-gray-100"
                              onClick={() =>
                                updateQuantity(
                                  dispatch,
                                  item,
                                  item.quantity + 1,
                                )
                              }
                            >
                              +
                            </button>
                          </div>

                          <button
                            className="text-red-500 hover:text-red-700 text-sm"
                            onClick={() => removeFromCart(dispatch, item)}
                          >
                            Remove
                          </button>
                        </div>
                      </div>
                    </div>
                  );
                })}

                {/* Shipping block */}
                <div className="p-4">
                  <h3 className="font-medium mb-2">Shipping</h3>

                  {computedOrder === undefined ? (
                    <div className="text-sm text-gray-600">
                      Calculating shipping...
                    </div>
                  ) : computedOrder.available === false ? (
                    <div className="bg-yellow-50 border border-yellow-100 rounded p-3 text-sm">
                      <div className="font-medium text-yellow-700">
                        Shipping information unavailable
                      </div>
                      <div className="mt-1 text-gray-700">
                        Shipping rates for these items are not available via the
                        packer. Please contact customer support.
                      </div>
                    </div>
                  ) : (
                    <div className="bg-blue-50 border border-blue-100 rounded p-3 text-sm">
                      <div className="font-medium">
                        Delivery to {computedOrder.country}
                      </div>
                      <div className="text-xs text-neutral-600">
                        Smart Consolidation - AI Powered
                      </div>

                      {computedOrder.parcels &&
                      computedOrder.parcels.length > 0 ? (
                        <div className="text-sm mt-2">
                          <div className="font-medium mb-2">
                            Shipping breakdown:
                          </div>
                          {computedOrder.parcels!.map(
                            (parcel: any, idx: number) => {
                              let parcelPriceGbp = parcel.basePriceGbp || 0;
                              if (parcel.tier) {
                                if (
                                  computedOrder.country === "Ghana" &&
                                  typeof parcel.tier.ghPriceGbp === "number"
                                ) {
                                  parcelPriceGbp = parcel.tier.ghPriceGbp;
                                } else if (
                                  computedOrder.country === "Nigeria" &&
                                  typeof parcel.tier.ngPriceGbp === "number"
                                ) {
                                  parcelPriceGbp = parcel.tier.ngPriceGbp;
                                } else if (
                                  typeof parcel.tier.priceGbp === "number"
                                ) {
                                  parcelPriceGbp = parcel.tier.priceGbp;
                                }
                              }
                              const convertedParcelPrice =
                                isLoadingExchangeRate || !exchangeRate
                                  ? parcelPriceGbp
                                  : roundToCurrency(
                                      parcelPriceGbp * exchangeRate,
                                      displayCurrencyCode,
                                    );
                              const vol = parcel.totalVolumeCm3 || 0;
                              const wt = parcel.totalWeightKg || 0;

                              return (
                                <div key={idx} className="mb-2">
                                  <div className="flex justify-between">
                                    <div>
                                      Parcel {idx + 1} ({parcel.items.length}{" "}
                                      {parcel.items.length !== 1
                                        ? "items"
                                        : "item"}
                                      ) :
                                    </div>
                                    <div className="font-medium">
                                      {isLoadingExchangeRate &&
                                      displayCurrencyCode !== "GBP" ? (
                                        <Skeleton className="w-24 h-6" />
                                      ) : (
                                        formatCurrency(
                                          convertedParcelPrice ||
                                            parcelPriceGbp,
                                          displayCurrencyCode || "GBP",
                                        )
                                      )}
                                    </div>
                                  </div>
                                  <div className="text-xs text-neutral-600 mt-1">
                                    {parcel.tier
                                      ? `${parcel.tier.name} • ${Number(wt).toFixed(2)} kg`
                                      : `${Number(wt).toFixed(2)} kg`}
                                  </div>
                                </div>
                              );
                            },
                          )}

                          <div className="flex justify-between mt-2">
                            <div className="font-medium">Duty:</div>
                            <div className="font-medium">
                              {isLoadingExchangeRate &&
                              displayCurrencyCode !== "GBP" ? (
                                <Skeleton className="w-24 h-6" />
                              ) : (
                                displayAmount(computedOrder.duty_gbp)
                              )}
                            </div>
                          </div>

                          <div className="flex justify-between mt-1 font-bold">
                            <div>Total:</div>
                            <div>
                              {displayAmount(
                                computedOrder.shipping_gbp +
                                  computedOrder.duty_gbp,
                              )}
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="text-sm">
                          {displayAmount(computedOrder.shipping_gbp)} •
                          Estimated delivery in {computedOrder.days || "N/A"}{" "}
                          days
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Subtotal block */}
                <div className="p-4 bg-gray-50">
                  <div className="flex justify-between mb-1">
                    <span>
                      Subtotal (
                      {cartItems.reduce((s, it) => s + it.quantity, 0)} items):
                    </span>
                    <span>{displayAmount(subtotalGbp)}</span>
                  </div>

                  <div className="flex justify-between mb-1">
                    <span>Shipping:</span>
                    <span>
                      {displayAmount(computedOrder?.shipping_gbp || 0)}
                    </span>
                  </div>

                  {computedOrder?.duty_gbp ? (
                    <div className="flex justify-between mb-1">
                      <span>Duty:</span>
                      <span>{displayAmount(computedOrder.duty_gbp)}</span>
                    </div>
                  ) : null}

                  <div className="flex justify-between font-bold">
                    <span>Total:</span>
                    <span>
                      {displayAmount(
                        (computedOrder?.shipping_gbp || 0) +
                          (computedOrder?.duty_gbp || 0) +
                          subtotalGbp,
                      )}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="lg:col-span-1">
            <div className="border rounded-lg p-4 shadow-sm lg:sticky lg:top-4">
              <h2 className="text-xl font-bold mb-4">Order Summary</h2>

              {areAllAddressFlagsNull() && (
                <Alert className="border-orange-200 bg-orange-50 mb-4">
                  <AlertDescription className="text-orange-800">
                    Please update your personal address details to proceed with
                    checkout.{" "}
                    <Link
                      href="/home/<USER>"
                      className="underline font-medium text-orange-800"
                    >
                      Update address
                    </Link>
                  </AlertDescription>
                </Alert>
              )}

              {userAddress?.DeliverTo && !isAddressInSupportedCountry() && (
                <Alert className="border-red-200 bg-red-50 mb-4">
                  <AlertDescription className="text-red-800">
                    Shipping is only available to Ghana and Nigeria. Please
                    update/change your personal address.{" "}
                    <Link
                      href="/home/<USER>"
                      className="underline font-medium text-red-800"
                    >
                      Change address
                    </Link>
                  </AlertDescription>
                </Alert>
              )}

              <div className="space-y-2 mb-4">
                <div className="flex justify-between">
                  <span>Items Subtotal:</span>
                  <span>{displayAmount(subtotalGbp)}</span>
                </div>

                <div className="border-t my-2 pt-2">
                  <div className="flex justify-between">
                    <span>Total Shipping (inc. duty):</span>
                    <span>
                      {computedOrder ? (
                        displayAmount(
                          (computedOrder.shipping_gbp || 0) +
                            (computedOrder.duty_gbp || 0),
                        )
                      ) : (
                        <Skeleton className="h-4 w-20" />
                      )}
                      {computedOrder && computedOrder.available === false && (
                        <span className="text-yellow-600 text-sm ml-1">
                          {" "}
                          (unavailable)
                        </span>
                      )}
                    </span>
                  </div>
                </div>

                <div className="border-t my-2 pt-2 text-xl font-bold">
                  <div className="flex justify-between">
                    <span>Grand Total:</span>
                    <span>
                      {computedOrder ? (
                        displayAmount(
                          subtotalGbp +
                            computedOrder.shipping_gbp +
                            computedOrder.duty_gbp,
                        )
                      ) : (
                        <Skeleton className="h-4 w-[150px]" />
                      )}
                    </span>
                  </div>
                  <div className="flex justify-end text-[14px] text-gray-600">
                    <span>
                      {formatCurrency(
                        subtotalGbp +
                          (computedOrder?.shipping_gbp || 0) +
                          (computedOrder?.duty_gbp || 0),
                        "GBP",
                      )}
                    </span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 p-3 rounded-md mb-4">
                <div className="flex items-start mb-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="w-5 h-5 text-blue-600 mr-2 flex-shrink-0 mt-0.5"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z"
                    />
                  </svg>
                  <div className="text-sm text-gray-700">
                    Shipping costs are calculated based on your delivery
                    location ({userAddress?.DeliverTo || "United Kingdom"}).
                  </div>
                </div>

                <div className="flex items-start mb-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="w-5 h-5 text-blue-600 mr-2 flex-shrink-0 mt-0.5"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M8.25 18.75a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 01-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 00-3.213-9.193 2.056 2.056 0 00-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 00-10.026 0 1.106 1.106 0 00-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"
                    />
                  </svg>
                  <div className="text-sm text-gray-700">
                    Items will be packed and shipped together.
                  </div>
                </div>

                <div className="flex items-start mt-2">
                  <TriangleAlert className="h-5 w-5 text-yellow-600 mr-2 flex-shrink-0 mt-0.5" />
                  <div className="text-sm text-yellow-700">
                    Please ensure your personal address is accurate at the time
                    of placing your order. Any additional delivery costs or
                    issues arising from incorrect or incomplete address details
                    will be the responsibility of the customer.
                  </div>
                </div>
              </div>

              <button
                className={`w-full py-3 text-white rounded-md font-medium ${
                  isLoadingUser ||
                  !user ||
                  areAllAddressFlagsNull() ||
                  !isAddressInSupportedCountry() ||
                  computedOrder?.available === false
                    ? "bg-gray-400 cursor-not-allowed"
                    : "bg-blue-600 hover:bg-blue-700"
                }`}
                onClick={handleProceedToPayment}
                disabled={
                  isLoadingUser ||
                  !user ||
                  areAllAddressFlagsNull() ||
                  !isAddressInSupportedCountry() ||
                  computedOrder?.available === false
                }
              >
                {isLoadingUser
                  ? "Loading..."
                  : areAllAddressFlagsNull()
                    ? "Update Address to Continue"
                    : !isAddressInSupportedCountry()
                      ? "Update/Change Address"
                      : computedOrder?.available === false
                        ? "Shipping Unavailable"
                        : "Proceed to Payment"}
              </button>

              <div className="mt-4 text-xs text-gray-600">
                <p>
                  By proceeding, you agree to our Terms of Service and Privacy
                  Policy.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Stripe Payment Modal */}
      {cartItems.length > 0 &&
        user &&
        computedOrder &&
        computedOrder.available !== false &&
        (() => {
          // Build the payment/order payload once and reuse for props so duty is consistently passed.
          const paymentOrder = buildOrderForPayment();
          return (
            <StripePaymentOrder
              isOpen={isPaymentModalOpen}
              onClose={handleClosePaymentModal}
              onSuccess={handlePaymentSuccess}
              userId={user.id!}
              email={user.email!}
              items={paymentOrder.orderItems}
              subtotal={paymentOrder.subtotal}
              taxAmount={paymentOrder.taxAmount}
              shippingCost={paymentOrder.shippingCost}
              duty={paymentOrder.duty}
              totalAmount={paymentOrder.totalAmount}
              currency={paymentOrder.currency}
            />
          );
        })()}
    </div>
  );
};

export default CheckoutPage;
